#!/usr/bin/env python3
"""
对比顺序循环 vs 随机搜索模式的性能差异
"""

import os
import subprocess
import time
import json
from datetime import datetime

def run_benchmark_test(search_pattern, concurrency, duration=60):
    """运行基准测试"""
    print(f"\n🧪 测试 {search_pattern} 搜索模式，并发数: {concurrency}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['DATASET_LOCAL_DIR'] = '/nas/yvan.chen/milvus/dataset'
    env['SEARCH_PATTERN'] = search_pattern
    
    # 构建命令
    cmd = [
        'python', '-m', 'vectordb_bench.cli.vectordbbench',
        'faissremote',
        '--uri', 'http://localhost:8005',
        '--case-type', 'Performance768D10M',
        '--index-type', 'HNSW',
        '--m', '30',
        '--ef-construction', '360',
        '--concurrency-duration', str(duration),
        '--num-concurrency', str(concurrency),
        '--skip-load',
        '--skip-search-serial'
    ]
    
    start_time = time.time()
    try:
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=duration + 60  # 额外60秒超时
        )
        
        end_time = time.time()
        
        return {
            'search_pattern': search_pattern,
            'concurrency': concurrency,
            'duration': end_time - start_time,
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'success': result.returncode == 0
        }
        
    except subprocess.TimeoutExpired:
        return {
            'search_pattern': search_pattern,
            'concurrency': concurrency,
            'duration': duration + 60,
            'returncode': -1,
            'stdout': '',
            'stderr': 'Timeout',
            'success': False
        }

def extract_performance_metrics(output):
    """从输出中提取性能指标"""
    metrics = {
        'qps': 0,
        'p99_latency': 0,
        'avg_latency': 0,
        'total_queries': 0
    }
    
    lines = output.split('\n')
    for line in lines:
        if 'QPS:' in line or 'qps=' in line:
            try:
                # 尝试提取QPS值
                if 'QPS:' in line:
                    qps_str = line.split('QPS:')[1].strip().split()[0]
                elif 'qps=' in line:
                    qps_str = line.split('qps=')[1].strip().split()[0]
                metrics['qps'] = float(qps_str)
            except:
                pass
        
        if 'P99' in line and 'latency' in line:
            try:
                # 提取P99延迟
                parts = line.split()
                for i, part in enumerate(parts):
                    if 'P99' in part and i + 1 < len(parts):
                        metrics['p99_latency'] = float(parts[i + 1].replace('ms', ''))
            except:
                pass
    
    return metrics

def compare_search_patterns():
    """对比不同搜索模式"""
    print("🔍 向量数据库搜索模式性能对比")
    print("=" * 60)
    
    test_configs = [
        {'concurrency': 16, 'duration': 30},
        {'concurrency': 32, 'duration': 30},
        {'concurrency': 64, 'duration': 30},
    ]
    
    results = []
    
    for config in test_configs:
        concurrency = config['concurrency']
        duration = config['duration']
        
        print(f"\n📊 测试配置: {concurrency}并发, {duration}秒")
        print("-" * 40)
        
        # 测试顺序循环模式
        sequential_result = run_benchmark_test('sequential', concurrency, duration)
        sequential_metrics = extract_performance_metrics(sequential_result['stdout'])
        
        time.sleep(5)  # 间隔5秒
        
        # 测试随机搜索模式
        random_result = run_benchmark_test('random', concurrency, duration)
        random_metrics = extract_performance_metrics(random_result['stdout'])
        
        # 对比结果
        comparison = {
            'concurrency': concurrency,
            'duration': duration,
            'sequential': {
                'success': sequential_result['success'],
                'metrics': sequential_metrics
            },
            'random': {
                'success': random_result['success'],
                'metrics': random_metrics
            }
        }
        
        results.append(comparison)
        
        # 打印对比结果
        print(f"📈 结果对比:")
        if sequential_result['success'] and random_result['success']:
            seq_qps = sequential_metrics['qps']
            rand_qps = random_metrics['qps']
            
            print(f"   顺序循环模式: QPS={seq_qps:.1f}")
            print(f"   随机搜索模式: QPS={rand_qps:.1f}")
            
            if seq_qps > 0 and rand_qps > 0:
                improvement = ((seq_qps - rand_qps) / rand_qps) * 100
                print(f"   性能差异: {improvement:+.1f}% (顺序相对随机)")
                
                if improvement > 20:
                    print(f"   ⚠️ 顺序模式性能过高，可能不真实")
                elif improvement > 0:
                    print(f"   ✅ 顺序模式略优，符合预期")
                else:
                    print(f"   🤔 随机模式更优，需要分析")
        else:
            print(f"   ❌ 测试失败")
            if not sequential_result['success']:
                print(f"      顺序模式失败: {sequential_result['stderr']}")
            if not random_result['success']:
                print(f"      随机模式失败: {random_result['stderr']}")
    
    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"search_pattern_comparison_{timestamp}.json"
    
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 详细结果已保存到: {result_file}")
    
    # 生成总结报告
    generate_summary_report(results)

def generate_summary_report(results):
    """生成总结报告"""
    print(f"\n📋 搜索模式对比总结")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['sequential']['success'] and r['random']['success']]
    
    if not successful_tests:
        print("❌ 没有成功的测试结果")
        return
    
    print(f"✅ 成功测试: {len(successful_tests)}/{len(results)}")
    print()
    
    avg_sequential_qps = sum(r['sequential']['metrics']['qps'] for r in successful_tests) / len(successful_tests)
    avg_random_qps = sum(r['random']['metrics']['qps'] for r in successful_tests) / len(successful_tests)
    
    print(f"📊 平均性能:")
    print(f"   顺序循环模式: {avg_sequential_qps:.1f} QPS")
    print(f"   随机搜索模式: {avg_random_qps:.1f} QPS")
    
    if avg_random_qps > 0:
        overall_diff = ((avg_sequential_qps - avg_random_qps) / avg_random_qps) * 100
        print(f"   整体性能差异: {overall_diff:+.1f}%")
    
    print(f"\n💡 建议:")
    if avg_sequential_qps > avg_random_qps * 1.2:
        print("   🎯 顺序模式性能明显更高，建议使用随机模式获得更真实的基准测试结果")
        print("   📈 随机模式更能反映真实应用场景的性能表现")
    elif avg_sequential_qps > avg_random_qps * 1.1:
        print("   ✅ 顺序模式略优，但差异不大，可以考虑使用随机模式")
    else:
        print("   🤔 两种模式性能相近，可能需要更深入的分析")
    
    print(f"\n🔧 配置建议:")
    print(f"   export SEARCH_PATTERN=random  # 使用随机搜索模式")
    print(f"   export SEARCH_PATTERN=sequential  # 使用顺序循环模式")

if __name__ == "__main__":
    compare_search_patterns()
