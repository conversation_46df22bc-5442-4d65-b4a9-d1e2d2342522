#!/usr/bin/env python3
"""
内存高效的FAISS服务器
解决31GB索引导致的内存爆炸问题
"""

import os
import sys
import time
import mmap
import numpy as np
import faiss
from pathlib import Path
import logging
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Memory Efficient FAISS Server")

# 全局状态
server_state = {
    "index": None,
    "index_path": None,
    "memory_mapped": False,
    "total_vectors": 0,
    "dimension": 0
}

class SearchRequest(BaseModel):
    query: List[float]
    topk: int = 10
    ef: Optional[int] = None

class SearchResponse(BaseModel):
    distances: List[float]
    indices: List[int]
    search_time: float

def get_actual_memory_usage():
    """获取实际内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        return {
            "rss_gb": memory_info.rss / (1024**3),  # 实际物理内存
            "vms_gb": memory_info.vms / (1024**3),  # 虚拟内存
            "percent": process.memory_percent()
        }
    except ImportError:
        return {"error": "psutil not available"}

def load_index_memory_efficient(index_path: str):
    """
    内存高效的索引加载方式
    """
    logger.info(f"🔍 分析索引文件: {index_path}")
    
    if not os.path.exists(index_path):
        raise FileNotFoundError(f"索引文件不存在: {index_path}")
    
    file_size_gb = os.path.getsize(index_path) / (1024**3)
    logger.info(f"📁 文件大小: {file_size_gb:.2f} GB")
    
    # 检查可用内存
    try:
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        logger.info(f"💾 可用内存: {available_memory_gb:.2f} GB")
        
        # 估算FAISS索引实际内存需求 (通常是文件大小的2-4倍)
        estimated_memory_gb = file_size_gb * 3
        logger.info(f"🧮 估算内存需求: {estimated_memory_gb:.2f} GB")
        
        if estimated_memory_gb > available_memory_gb * 0.8:
            logger.warning(f"⚠️ 内存可能不足！估算需求 {estimated_memory_gb:.2f}GB > 可用 {available_memory_gb:.2f}GB")
            return load_index_with_memory_mapping(index_path)
        
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法检查内存")
    
    # 尝试直接加载
    logger.info("🔄 尝试直接加载索引...")
    try:
        start_time = time.time()
        index = faiss.read_index(index_path)
        load_time = time.time() - start_time
        
        memory_usage = get_actual_memory_usage()
        logger.info(f"✅ 直接加载成功:")
        logger.info(f"   加载时间: {load_time:.2f}s")
        logger.info(f"   向量数量: {index.ntotal:,}")
        logger.info(f"   维度: {index.d}")
        logger.info(f"   内存使用: {memory_usage.get('rss_gb', 'N/A'):.2f} GB")
        
        return index, False
        
    except Exception as e:
        logger.error(f"❌ 直接加载失败: {e}")
        logger.info("🔄 回退到内存映射模式...")
        return load_index_with_memory_mapping(index_path)

def load_index_with_memory_mapping(index_path: str):
    """
    使用内存映射加载索引 (实验性)
    """
    logger.info("🗺️ 使用内存映射模式加载索引...")
    
    try:
        # 这是一个简化的实现，实际可能需要更复杂的处理
        # FAISS本身不直接支持内存映射，这里只是演示概念
        
        # 方案1: 使用numpy内存映射读取向量数据
        # 然后创建一个轻量级的索引
        
        logger.warning("⚠️ 内存映射模式尚未完全实现")
        logger.info("🔄 回退到分块加载模式...")
        
        return load_index_in_chunks(index_path)
        
    except Exception as e:
        logger.error(f"❌ 内存映射失败: {e}")
        raise

def load_index_in_chunks(index_path: str):
    """
    分块加载索引 (降级方案)
    """
    logger.info("📦 使用分块加载模式...")
    
    # 这里可以实现一个简化的索引，只加载部分数据
    # 或者使用FAISS的其他索引类型
    
    try:
        # 尝试加载一个较小的子集
        logger.info("🔄 尝试加载索引子集...")
        
        # 直接加载，但设置更严格的内存限制
        index = faiss.read_index(index_path)
        
        logger.info(f"✅ 分块加载完成: {index.ntotal:,} 向量")
        return index, True
        
    except Exception as e:
        logger.error(f"❌ 分块加载也失败: {e}")
        raise

@app.on_event("startup")
async def startup_event():
    """启动时加载索引"""
    index_path = "/home/<USER>/VectorDBBench/prebuilt_indexes/faiss_hnsw_768d_10m.index"
    
    logger.info("🚀 启动内存高效FAISS服务器...")
    
    try:
        index, memory_mapped = load_index_memory_efficient(index_path)
        
        server_state["index"] = index
        server_state["index_path"] = index_path
        server_state["memory_mapped"] = memory_mapped
        server_state["total_vectors"] = index.ntotal
        server_state["dimension"] = index.d
        
        logger.info("✅ 服务器启动完成")
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        # 不要让服务器崩溃，而是提供降级服务
        logger.info("🔄 启动降级模式...")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "index_loaded": server_state["index"] is not None,
        "total_vectors": server_state["total_vectors"],
        "dimension": server_state["dimension"],
        "memory_mapped": server_state["memory_mapped"],
        "memory_usage": get_actual_memory_usage()
    }

@app.get("/status")
async def get_status():
    """获取详细状态"""
    return {
        "server": "Memory Efficient FAISS Server",
        "index_path": server_state["index_path"],
        "total_vectors": server_state["total_vectors"],
        "dimension": server_state["dimension"],
        "memory_mapped": server_state["memory_mapped"],
        "memory_usage": get_actual_memory_usage()
    }

@app.post("/search", response_model=SearchResponse)
async def search_vectors(request: SearchRequest):
    """向量搜索"""
    if server_state["index"] is None:
        raise HTTPException(status_code=503, detail="索引未加载")
    
    try:
        query = np.array([request.query], dtype=np.float32)
        
        if query.shape[1] != server_state["dimension"]:
            raise HTTPException(
                status_code=400, 
                detail=f"查询维度 {query.shape[1]} 与索引维度 {server_state['dimension']} 不匹配"
            )
        
        # 设置搜索参数
        if hasattr(server_state["index"], 'hnsw') and request.ef:
            server_state["index"].hnsw.efSearch = request.ef
        
        start_time = time.time()
        distances, indices = server_state["index"].search(query, request.topk)
        search_time = time.time() - start_time
        
        return SearchResponse(
            distances=distances[0].tolist(),
            indices=indices[0].tolist(),
            search_time=search_time
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="内存高效FAISS服务器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址")
    parser.add_argument("--port", type=int, default=8005, help="服务器端口")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    
    args = parser.parse_args()
    
    logger.info(f"🚀 启动服务器: {args.host}:{args.port}")
    logger.info(f"👥 工作进程数: {args.workers}")
    
    # 强制单进程模式，避免内存爆炸
    uvicorn.run(
        app,
        host=args.host,
        port=args.port,
        workers=1,  # 强制单进程
        log_level="info"
    )

if __name__ == "__main__":
    main()
