#!/usr/bin/env python3
"""
将VectorDBBench的搜索模式从顺序循环改为纯随机搜索
"""

import re
import os
import shutil
from pathlib import Path

def convert_sequential_to_random(file_path):
    """将文件中的顺序搜索改为随机搜索"""
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 备份原文件
    backup_path = f"{file_path}.backup_sequential"
    shutil.copy2(file_path, backup_path)
    print(f"📁 已备份原文件到: {backup_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 替换初始化模式
        # 从: num, idx = len(test_data), random.randint(0, len(test_data) - 1)
        # 到: num = len(test_data)
        pattern1 = r'num,\s*idx\s*=\s*len\(test_data\),\s*random\.randint\(0,\s*len\(test_data\)\s*-\s*1\)'
        replacement1 = 'num = len(test_data)'
        content = re.sub(pattern1, replacement1, content)
        
        # 2. 在搜索调用前添加随机索引选择
        # 查找 self.db.search_embedding 调用
        pattern2 = r'(\s+)(self\.db\.search_embedding\(\s*test_data\[idx\])'
        replacement2 = r'\1# 🎲 每次随机选择查询向量\n\1idx = random.randint(0, num - 1)\n\1\2'
        content = re.sub(pattern2, replacement2, content)
        
        # 3. 删除顺序循环逻辑
        # 删除: idx = idx + 1 if idx < num - 1 else 0
        pattern3 = r'\s*#?\s*loop through the test data\s*\n\s*idx\s*=\s*idx\s*\+\s*1\s*if\s*idx\s*<\s*num\s*-\s*1\s*else\s*0\s*\n?'
        content = re.sub(pattern3, '\n', content)
        
        # 4. 删除单独的顺序循环行
        pattern4 = r'\s*idx\s*=\s*idx\s*\+\s*1\s*if\s*idx\s*<\s*num\s*-\s*1\s*else\s*0\s*\n'
        content = re.sub(pattern4, '', content)
        
        # 5. 添加import random（如果不存在）
        if 'import random' not in content:
            # 在其他import后添加
            import_pattern = r'(import\s+\w+.*\n)'
            if re.search(import_pattern, content):
                content = re.sub(r'(import\s+\w+.*\n)', r'\1import random\n', content, count=1)
            else:
                content = 'import random\n' + content
        
        # 检查是否有实际修改
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已将 {file_path} 转换为随机搜索模式")
            return True
        else:
            print(f"ℹ️ {file_path} 未发现需要修改的顺序搜索模式")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")
        # 恢复备份
        shutil.copy2(backup_path, file_path)
        return False

def find_and_convert_files():
    """查找并转换所有相关文件"""
    
    # 要搜索的文件模式
    search_patterns = [
        "vectordb_bench/backend/runner/*.py",
        "vectordb_bench/backend/clients/*/*.py",
        "*.py"  # 当前目录的Python文件
    ]
    
    converted_files = []
    
    for pattern in search_patterns:
        files = Path('.').glob(pattern)
        for file_path in files:
            if file_path.is_file() and file_path.suffix == '.py':
                # 检查文件是否包含顺序搜索模式
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否包含顺序循环模式
                    if 'idx = idx + 1 if idx < num - 1 else 0' in content:
                        print(f"\n🔍 发现顺序搜索模式: {file_path}")
                        if convert_sequential_to_random(str(file_path)):
                            converted_files.append(str(file_path))
                            
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    return converted_files

def create_test_script():
    """创建测试脚本来验证修改效果"""
    
    test_script = """#!/usr/bin/env python3
'''
测试随机搜索模式的效果
'''

import random
import time

def test_random_search_pattern():
    '''模拟随机搜索模式'''
    
    # 模拟测试数据
    test_data = [f"vector_{i}" for i in range(1000)]
    num = len(test_data)
    duration = 5  # 5秒测试
    
    print("🧪 测试随机搜索模式")
    print(f"   数据集大小: {num}")
    print(f"   测试时长: {duration}秒")
    
    start_time = time.perf_counter()
    count = 0
    used_indices = set()
    
    while time.perf_counter() < start_time + duration:
        # 🎲 随机选择索引
        idx = random.randint(0, num - 1)
        used_indices.add(idx)
        
        # 模拟搜索
        vector = test_data[idx]
        time.sleep(0.001)  # 模拟搜索延迟
        count += 1
    
    total_dur = time.perf_counter() - start_time
    coverage = len(used_indices) / num * 100
    
    print(f"\\n📊 测试结果:")
    print(f"   总搜索次数: {count}")
    print(f"   实际耗时: {total_dur:.2f}秒")
    print(f"   QPS: {count/total_dur:.1f}")
    print(f"   向量覆盖率: {coverage:.1f}% ({len(used_indices)}/{num})")
    print(f"   平均每向量搜索: {count/num:.1f}次")
    
    if coverage > 80:
        print("✅ 随机搜索覆盖率良好")
    else:
        print("⚠️ 随机搜索覆盖率较低，可能需要更长测试时间")

if __name__ == "__main__":
    test_random_search_pattern()
"""
    
    with open('test_random_search.py', 'w') as f:
        f.write(test_script)
    
    os.chmod('test_random_search.py', 0o755)
    print("📝 已创建测试脚本: test_random_search.py")

def main():
    """主函数"""
    print("🔄 VectorDBBench 搜索模式转换工具")
    print("=" * 50)
    print("将顺序循环搜索改为纯随机搜索")
    print()
    
    # 查找并转换文件
    converted_files = find_and_convert_files()
    
    print(f"\n📋 转换总结:")
    print(f"   转换文件数: {len(converted_files)}")
    
    if converted_files:
        print(f"   转换的文件:")
        for file_path in converted_files:
            print(f"     - {file_path}")
        
        print(f"\n💡 使用建议:")
        print(f"   1. 运行测试验证修改效果")
        print(f"   2. 对比修改前后的性能差异")
        print(f"   3. 如需恢复，使用 .backup_sequential 文件")
        
        # 创建测试脚本
        create_test_script()
        
    else:
        print("   ℹ️ 未发现需要转换的文件")
    
    print(f"\n🎯 修改要点:")
    print(f"   - 删除: idx = idx + 1 if idx < num - 1 else 0")
    print(f"   - 添加: idx = random.randint(0, num - 1)")
    print(f"   - 每次搜索都随机选择向量")

if __name__ == "__main__":
    main()
